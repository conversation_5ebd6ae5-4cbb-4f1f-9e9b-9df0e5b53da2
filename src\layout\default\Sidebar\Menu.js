import React, { useEffect } from "react";
import classNames from "classnames";

// import getParents from '../../../utilities/getParents';
import slideUp from "../../../utilities/slideUp";
import slideDown from "../../../utilities/slideDown";
import getParents from "../../../utilities/getParents";

import { NavLink, Link } from "react-router-dom";
import { useSelector } from "react-redux";

// function MenuHeading({className, text, ...props }) {
// const compClass = classNames({
//     'nk-menu-heading': true,
//     [className]: className
// });
//     return (
//         <li className={compClass}>
//             <h6 className="overline-title">{text || props.children}</h6>
//         </li>
//     )
// }

function MenuItemTemplate({ text, icon }) {
  return (
    <>
      {icon && (
        <span className="nk-menu-icon">
          <em className={`icon ni ni-${icon}`}></em>
        </span>
      )}
      {text && <span className="nk-menu-text">{text}</span>}
    </>
  );
}

function MenuItemLink({ text, icon, sub, to, blank, onClick }) {
  return (
    <>
      {!blank && !sub && (
        <NavLink className="nk-menu-link" to={to}>
          <MenuItemTemplate icon={icon} text={text} />
        </NavLink>
      )}
      {blank && (
        <Link className="nk-menu-link" to={to} target="_blank">
          <MenuItemTemplate icon={icon} text={text} />
        </Link>
      )}
      {sub && (
        <a
          className="nk-menu-link nk-menu-toggle"
          onClick={onClick}
          href="#expand"
        >
          <MenuItemTemplate icon={icon} text={text} />
        </a>
      )}
    </>
  );
}

function MenuItem({ sub, className, ...props }) {
  const compClass = classNames({
    "nk-menu-item": true,
    "has-sub": sub,
    [className]: className,
  });
  return <li className={compClass}>{props.children}</li>;
}

function MenuSub({ mega, className, ...props }) {
  const compClass = classNames({
    "nk-menu-sub": true,
    [className]: className,
  });
  return <ul className={compClass}>{props.children}</ul>;
}

function MenuList({ className, ...props }) {
  const compClass = classNames({
    "nk-menu": true,
    [className]: className,
  });
  return <ul className={compClass}>{props.children}</ul>;
}

function Menu() {
  const userData = useSelector(state => state.user);

  // variables for Sidebar
  let menu = {
    classes: {
      main: "nk-menu",
      item: "nk-menu-item",
      link: "nk-menu-link",
      toggle: "nk-menu-toggle",
      sub: "nk-menu-sub",
      subparent: "has-sub",
      active: "active",
      current: "current-page",
    },
  };

  let currentLink = function (selector) {
    let elm = document.querySelectorAll(selector);
    elm.forEach(function (item) {
      var activeRouterLink = item.classList.contains("active");
      if (activeRouterLink) {
        let parents = getParents(
          item,
          `.${menu.classes.main}`,
          menu.classes.item
        );
        parents.forEach((parentElemets) => {
          parentElemets.classList.add(
            menu.classes.active,
            menu.classes.current
          );
          let subItem = parentElemets.querySelector(`.${menu.classes.sub}`);
          subItem !== null && (subItem.style.display = "block");
        });
      } else {
        item.parentElement.classList.remove(
          menu.classes.active,
          menu.classes.current
        );
      }
    });
  };

  // dropdown toggle
  let dropdownToggle = function (elm) {
    let parent = elm.parentElement;
    let nextelm = elm.nextElementSibling;
    let speed =
      nextelm.children.length > 5 ? 400 + nextelm.children.length * 10 : 400;
    if (!parent.classList.contains(menu.classes.active)) {
      parent.classList.add(menu.classes.active);
      slideDown(nextelm, speed);
    } else {
      parent.classList.remove(menu.classes.active);
      slideUp(nextelm, speed);
    }
  };

  // dropdown close siblings
  let closeSiblings = function (elm) {
    let parent = elm.parentElement;
    let siblings = parent.parentElement.children;
    Array.from(siblings).forEach((item) => {
      if (item !== parent) {
        item.classList.remove(menu.classes.active);
        if (item.classList.contains(menu.classes.subparent)) {
          let subitem = item.querySelectorAll(`.${menu.classes.sub}`);
          subitem.forEach((child) => {
            child.parentElement.classList.remove(menu.classes.active);
            slideUp(child, 400);
          });
        }
      }
    });
  };

  let menuToggle = function (e) {
    e.preventDefault();
    let item = e.target.closest(`.${menu.classes.toggle}`);
    dropdownToggle(item);
    closeSiblings(item);
  };

  useEffect(() => {
    currentLink(`.${menu.classes.link}`);
    // eslint-disable-next-line
  }, [null]);
  return (
    <MenuList>
      {/* {userData?.accessScopes?.coach?.includes('read') && */}
      <MenuItem>
        <MenuItemLink text="Coach" to="/coach-page" />
      </MenuItem>
      {/* } */}
      {userData?.accessScopes?.course?.includes("read") && (
        <MenuItem>
          <MenuItemLink text="Training Schedule" to="/course-page" />
        </MenuItem>
      )}
      {userData?.accessScopes?.category?.includes("read") && (
        <MenuItem>
          <MenuItemLink text="Category" to="/category-list" />
        </MenuItem>
      )}
      {userData?.accessScopes?.booking?.includes("read") && (
        <MenuItem>
          <MenuItemLink text="Booking" to="/Booking" />
        </MenuItem>
      )}
      {userData?.accessScopes?.player?.includes("read") && (
        <MenuItem>
          <MenuItemLink text="Athlete" to="/player-list" />
        </MenuItem>
      )}
      {userData?.accessScopes?.cms?.includes("read") && (
        <MenuItem sub>
          <MenuItemLink text="CMS" onClick={menuToggle} sub />
          <MenuSub>
            <MenuItem>
              <MenuItemLink text="Top Courses" to="/cms/top-courses" />
            </MenuItem>
            <MenuItem>
              <MenuItemLink text="Top Coach" to="/cms/top-coaches" />
            </MenuItem>
            <MenuItem>
              <MenuItemLink text="Top Categories" to="/cms/top-categories" />
            </MenuItem>
            <MenuItem>
              <MenuItemLink text="Testimonials" to="/cms/testimonials" />
            </MenuItem>
            <MenuItem>
              <MenuItemLink
                text="Registration"
                to="/cms/instructor-registration"
              />
            </MenuItem>
            <MenuItem>
              <MenuItemLink
                text="Registration Details"
                to="/cms/registration-details"
              />
            </MenuItem>
            <MenuItem>
              <MenuItemLink
                text="Coach T&C"
                to="/cms/terms-and-condition-details"
              />
            </MenuItem>
            <MenuItem>
              <MenuItemLink
                text="Player T&C"
                to="/cms/player-terms-and-condition-details"
              />
            </MenuItem>
            <MenuItem>
              <MenuItemLink text="About Us" to="/cms/about-us" />
            </MenuItem>
            <MenuItem>
              <MenuItemLink text="Block" to="/cms/blocks" />
            </MenuItem>
            <MenuItem>
              <MenuItemLink text="Web Content" to="/cms/policy" />
            </MenuItem>
            <MenuItem>
              <MenuItemLink text="FAQ" to="/cms/faq" />
            </MenuItem>
          </MenuSub>
        </MenuItem>
      )}
      {(userData?.accessScopes?.user?.includes("read") ||
        userData?.accessScopes?.user_group?.includes("read")) && (
        <MenuItem sub>
          <MenuItemLink text="Administration" onClick={menuToggle} sub />
          <MenuSub>
            {userData?.accessScopes?.user?.includes("read") && (
              <MenuItem>
                <MenuItemLink text="User" to="/user" />
              </MenuItem>
            )}
            {userData?.accessScopes?.user_group?.includes("read") && (
              <MenuItem>
                <MenuItemLink text="Role" to="/user-role" />
              </MenuItem>
            )}
          </MenuSub>
        </MenuItem>
      )}
      {userData?.accessScopes?.contact?.includes("read") && (
        <MenuItem>
          <MenuItemLink text="Contact" to="/contact" />
        </MenuItem>
      )}
      {userData?.accessScopes?.reports?.includes("read") && (
        <MenuItem>
          <MenuItemLink text="Reports" to="/report" />
        </MenuItem>
      )}
    </MenuList>
  );
}

export default Menu;
